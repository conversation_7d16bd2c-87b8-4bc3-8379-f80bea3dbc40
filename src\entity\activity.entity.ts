import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface ActivityAttributes {
  /** 活动ID */
  id: number;
  /** 活动标题 */
  title: string;
  /** 内容类型（content-富文本，url-链接地址） */
  contentType: string;
  /** 富文本内容 */
  content?: string;
  /** 活动链接地址 */
  url?: string;
  /** 受众（用户端/员工端） */
  target: string;
  /** 封面图片链接 */
  coverImage?: string;
  /** 是否启用 */
  status: number;
  /** 是否发布 */
  isPublished: number;
  /** 发布时间 */
  publishedAt?: Date;
  /** 排序字段 */
  sortOrder: number;
}

@Table({ tableName: 'activities', timestamps: true, comment: '活动表' })
export class Activity
  extends Model<ActivityAttributes>
  implements ActivityAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '活动ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '活动标题',
  })
  title: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    defaultValue: 'content',
    comment: '内容类型（content-富文本，url-链接地址）',
  })
  contentType: string;

  @Column({
    type: DataType.TEXT,
    comment: '富文本内容',
  })
  content: string;

  @Column({
    type: DataType.STRING(500),
    comment: '活动链接地址',
  })
  url: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '受众（用户端/员工端）',
  })
  target: string;

  @Column({
    type: DataType.STRING(255),
    comment: '封面图片链接',
  })
  coverImage: string;

  @Column({
    type: DataType.TINYINT,
    defaultValue: 0,
    comment: '是否启用',
  })
  status: number;

  @Column({
    type: DataType.TINYINT,
    defaultValue: 0,
    comment: '是否发布（0-未发布，1-已发布）',
  })
  isPublished: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '发布时间',
  })
  publishedAt: Date;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '排序字段',
  })
  sortOrder: number;
}
