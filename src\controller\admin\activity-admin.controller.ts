import {
  Controller,
  Get,
  Post,
  Put,
  Del,
  Param,
  Query,
  Body,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { Context } from '@midwayjs/koa';
import { ActivityService } from '../../service/activity.service';
import { CustomError } from '../../error/custom.error';
import {
  CreateActivityDto,
  UpdateActivityDto,
  QueryActivityDto,
} from '../../dto/activity.dto';

@Controller('/admin/activities')
export class ActivityAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  activityService: ActivityService;

  @Get('/', { summary: '管理端查询活动列表' })
  @Validate()
  async findAll(@Query() queryDto: QueryActivityDto) {
    const {
      current = 1,
      pageSize = 10,
      title,
      target,
      status,
      isPublished,
    } = queryDto;
    return await this.activityService.findAllForAdmin(
      current,
      pageSize,
      title,
      target,
      status,
      isPublished
    );
  }

  @Get('/:id', { summary: '管理端查询活动详情' })
  async findById(@Param('id') id: number) {
    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }
    return activity;
  }

  @Post('/', { summary: '管理端创建活动' })
  @Validate()
  async create(@Body() createActivityDto: CreateActivityDto) {
    this.ctx.logger.info('【管理端创建活动】：', createActivityDto);

    // 如果没有指定排序，自动设置为最大值+1
    if (!createActivityDto.sortOrder) {
      const maxSortOrder = (await this.activityService
        .getModel()
        .max('sortOrder')) as number;
      createActivityDto.sortOrder = (maxSortOrder || 0) + 1;
    }

    const activity = await this.activityService.create({
      ...createActivityDto,
      isPublished: 0, // 新创建的活动默认未发布
      publishedAt: null,
    });

    return activity;
  }

  @Put('/:id', { summary: '管理端更新活动' })
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateActivityDto: UpdateActivityDto
  ) {
    this.ctx.logger.info('【管理端更新活动】：', { id, ...updateActivityDto });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    await this.activityService.update({ id }, updateActivityDto);
    return true;
  }

  @Del('/:id', { summary: '管理端删除活动' })
  async delete(@Param('id') id: number) {
    this.ctx.logger.info('【管理端删除活动】：', { id });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    // 如果活动已发布，不允许删除
    if (activity.isPublished === 1) {
      throw new CustomError('已发布的活动不能删除，请先取消发布');
    }

    await this.activityService.delete({ id });
    return true;
  }

  @Put('/:id/status', { summary: '管理端更新活动状态' })
  async updateStatus(
    @Param('id') id: number,
    @Body() { status }: { status: number }
  ) {
    this.ctx.logger.info('【管理端更新活动状态】：', { id, status });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    // 如果要禁用活动，且活动已发布，需要先取消发布
    if (status === 0 && activity.isPublished === 1) {
      await this.activityService.unpublishActivity(id);
    }

    await this.activityService.updateStatus(id, status);
    return true;
  }

  @Post('/:id/publish', { summary: '管理端发布活动' })
  async publishActivity(@Param('id') id: number) {
    this.ctx.logger.info('【管理端发布活动】：', { id });

    await this.activityService.publishActivity(id);
    return true;
  }

  @Post('/:id/unpublish', { summary: '管理端取消发布活动' })
  async unpublishActivity(@Param('id') id: number) {
    this.ctx.logger.info('【管理端取消发布活动】：', { id });

    await this.activityService.unpublishActivity(id);
    return true;
  }

  @Put('/:id/sort-order', { summary: '管理端更新活动排序' })
  async updateSortOrder(
    @Param('id') id: number,
    @Body() { sortOrder }: { sortOrder: number }
  ) {
    this.ctx.logger.info('【管理端更新活动排序】：', { id, sortOrder });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    await this.activityService.updateSortOrder(id, sortOrder);
    return true;
  }

  @Get('/published/current', { summary: '管理端查询当前发布的活动' })
  async getCurrentPublished(@Query('target') target = '用户端') {
    return await this.activityService.getCurrentPublishedActivity(target);
  }
}
