import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ActivityService } from '../../service/activity.service';

@Controller('/openapi/activities')
export class ActivityOpenApiController {
  @Inject()
  ctx: Context;

  @Inject()
  activityService: ActivityService;

  @Get('/current', { summary: '小程序端获取当前发布的活动' })
  async getCurrentPublished(@Query('target') target = '用户端') {
    this.ctx.logger.info('【小程序端获取当前发布的活动】：', { target });

    const activity = await this.activityService.getCurrentPublishedActivity(
      target
    );

    if (!activity) {
      return null;
    }

    // 只返回必要的字段给小程序端
    return {
      id: activity.id,
      title: activity.title,
      content: activity.content,
      coverImage: activity.coverImage,
      publishedAt: activity.publishedAt,
    };
  }

  @Get('/:id', { summary: '小程序端获取活动详情' })
  async getActivityDetail(@Query('id') id: number) {
    this.ctx.logger.info('【小程序端获取活动详情】：', { id });

    const activity = await this.activityService.findById(id);

    if (!activity || activity.status !== 1 || activity.isPublished !== 1) {
      return null;
    }

    // 只返回必要的字段给小程序端
    return {
      id: activity.id,
      title: activity.title,
      content: activity.content,
      coverImage: activity.coverImage,
      publishedAt: activity.publishedAt,
    };
  }
}
