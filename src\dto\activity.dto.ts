import { Rule, RuleType } from '@midwayjs/validate';
import { BaseQueryDTO } from './BaseDTO';

/**
 * 查询活动DTO
 */
export class QueryActivityDto extends BaseQueryDTO {
  @Rule(RuleType.string().optional().error(new Error('活动标题必须是字符串')))
  title?: string;

  @Rule(RuleType.string().optional().error(new Error('受众必须是字符串')))
  target?: string;

  @Rule(RuleType.number().optional().error(new Error('状态必须是数字')))
  status?: number;

  @Rule(RuleType.number().optional().error(new Error('发布状态必须是数字')))
  isPublished?: number;
}

/**
 * 创建活动DTO
 */
export class CreateActivityDto {
  @Rule(
    RuleType.string()
      .required()
      .max(100)
      .error(new Error('活动标题不能为空且不能超过100字符'))
  )
  /** 活动标题 */
  title: string;

  @Rule(RuleType.string().required().error(new Error('富文本内容不能为空')))
  /** 富文本内容 */
  content: string;

  @Rule(
    RuleType.string()
      .required()
      .valid('用户端', '员工端')
      .error(new Error('受众必须是用户端或员工端'))
  )
  /** 受众（用户端/员工端） */
  target: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(255)
      .error(new Error('封面图片链接不能超过255字符'))
  )
  /** 封面图片链接 */
  coverImage?: string;

  @Rule(
    RuleType.number()
      .optional()
      .valid(0, 1)
      .default(1)
      .error(new Error('状态必须是0或1'))
  )
  /** 是否启用 */
  status?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .error(new Error('排序字段必须大于等于0'))
  )
  /** 排序字段 */
  sortOrder?: number;
}

/**
 * 更新活动DTO
 */
export class UpdateActivityDto {
  @Rule(
    RuleType.string()
      .optional()
      .max(100)
      .error(new Error('活动标题不能超过100字符'))
  )
  /** 活动标题 */
  title?: string;

  @Rule(RuleType.string().optional().error(new Error('富文本内容必须是字符串')))
  /** 富文本内容 */
  content?: string;

  @Rule(
    RuleType.string()
      .optional()
      .valid('用户端', '员工端')
      .error(new Error('受众必须是用户端或员工端'))
  )
  /** 受众（用户端/员工端） */
  target?: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(255)
      .error(new Error('封面图片链接不能超过255字符'))
  )
  /** 封面图片链接 */
  coverImage?: string;

  @Rule(
    RuleType.number().optional().valid(0, 1).error(new Error('状态必须是0或1'))
  )
  /** 是否启用 */
  status?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .error(new Error('排序字段必须大于等于0'))
  )
  /** 排序字段 */
  sortOrder?: number;
}
