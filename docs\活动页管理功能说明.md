# 活动页管理功能说明

## 功能概述

小程序活动页管理功能，支持富文本内容或URL链接两种方式维护活动信息列表，同一时间只能发布一个活动。

## 核心特性

### 1. 活动内容管理
- **双内容模式**：支持富文本内容和URL链接两种内容类型
  - `content` 模式：使用富文本编辑器编辑活动详情
  - `url` 模式：配置外部链接地址，跳转到指定页面
- **受众分类**：支持用户端和员工端两种受众类型
- **状态管理**：支持启用/禁用状态控制
- **排序功能**：支持自定义排序，便于管理活动显示顺序

### 2. 发布控制（核心约束）
- **唯一发布约束**：同一受众类型（用户端/员工端）同时只能有一个活动处于发布状态
- **自动切换机制**：发布新活动时自动取消同受众的其他已发布活动
- **跨受众独立**：用户端和员工端的活动发布状态相互独立，可以同时各自发布一个活动
- **发布条件**：只有启用状态的活动才能发布
- **发布时间**：记录活动发布时间，便于追踪

### 3. 内容验证规则
- **富文本模式**：选择 `content` 类型时，富文本内容不能为空
- **链接模式**：选择 `url` 类型时，链接地址不能为空且必须是有效的HTTP/HTTPS地址
- **格式验证**：URL必须以 `http://` 或 `https://` 开头

### 4. 权限控制
- **删除限制**：已发布的活动不能直接删除，需要先取消发布
- **状态联动**：禁用已发布的活动时，系统会自动先取消发布

## 数据库设计

### activities 表结构
```sql
CREATE TABLE `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `title` varchar(100) NOT NULL COMMENT '活动标题',
  `content` text COMMENT '富文本内容',
  `contentType` varchar(20) NOT NULL DEFAULT 'content' COMMENT '内容类型（content-富文本，url-链接地址）',
  `url` varchar(500) DEFAULT NULL COMMENT '活动链接地址',
  `target` varchar(20) NOT NULL COMMENT '受众（用户端/员工端）',
  `coverImage` varchar(255) DEFAULT NULL COMMENT '封面图片链接',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否启用',
  `isPublished` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否发布（0-未发布，1-已发布）',
  `publishedAt` datetime DEFAULT NULL COMMENT '发布时间',
  `sortOrder` int(11) NOT NULL DEFAULT '0' COMMENT '排序字段',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_activities_target_published` (`target`,`isPublished`),
  KEY `idx_activities_sort_order` (`sortOrder`),
  KEY `idx_activities_published_at` (`publishedAt`),
  KEY `idx_activities_content_type` (`contentType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='活动表';
```

## 核心文件

### 实体类
- **活动实体**：`src/entity/activity.entity.ts`

### 服务类
- **活动服务**：`src/service/activity.service.ts`

### 控制器
- **管理端控制器**：`src/controller/admin/activity-admin.controller.ts`
- **小程序端控制器**：`src/controller/openapi/activity.controller.ts`

### DTO验证
- **活动DTO**：`src/dto/activity.dto.ts`

## API接口

### 管理端接口
- `GET /admin/activities` - 查询活动列表
- `GET /admin/activities/:id` - 查询活动详情
- `POST /admin/activities` - 创建活动
- `PUT /admin/activities/:id` - 更新活动
- `DELETE /admin/activities/:id` - 删除活动
- `PUT /admin/activities/:id/status` - 更新活动状态
- `POST /admin/activities/:id/publish` - 发布活动
- `POST /admin/activities/:id/unpublish` - 取消发布活动
- `PUT /admin/activities/:id/sort-order` - 更新活动排序
- `GET /admin/activities/published/current` - 查询当前发布的活动

### 小程序端接口
- `GET /openapi/activities/current` - 获取当前发布的活动
- `GET /openapi/activities/:id` - 获取活动详情

## 部署说明

### 1. 数据库迁移
执行数据库迁移脚本添加新字段：
```bash
# 连接到数据库
mysql -u username -p database_name

# 执行脚本
source docs/活动页管理数据库脚本.sql
```

### 2. 代码部署
新增的文件已经通过实体导出文件自动注册，无需额外配置。

### 3. 权限配置
根据需要为管理端配置相应的接口访问权限。

## 使用示例

### 管理端创建富文本活动
```javascript
const activity = await fetch('/admin/activities', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    title: '春节特惠活动',
    contentType: 'content',
    content: '<h1>春节特惠</h1><p>活动详情...</p>',
    target: '用户端',
    coverImage: 'https://example.com/cover.jpg',
    status: 1,
  }),
});
```

### 管理端创建链接活动
```javascript
const activity = await fetch('/admin/activities', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    title: '外部活动页面',
    contentType: 'url',
    url: 'https://example.com/activity-page',
    target: '用户端',
    coverImage: 'https://example.com/cover.jpg',
    status: 1,
  }),
});
```

### 小程序端获取活动
```javascript
// 获取当前发布的活动
const response = await fetch('/openapi/activities/current?target=用户端');
const activity = await response.json();

if (activity.data) {
  console.log('当前活动:', activity.data.title);
  console.log('内容类型:', activity.data.contentType);
  
  if (activity.data.contentType === 'content') {
    // 显示富文本内容
    console.log('富文本内容:', activity.data.content);
  } else if (activity.data.contentType === 'url') {
    // 跳转到外部链接
    console.log('跳转链接:', activity.data.url);
  }
}
```

## 业务流程

### 活动发布流程
1. 管理员创建活动（选择内容类型：富文本或链接）
2. 根据内容类型填写相应内容
3. 设置封面图片和其他属性
4. 启用活动状态
5. 发布活动（系统自动取消同受众的其他已发布活动）
6. 小程序端可获取到当前发布的活动

### 活动管理流程
1. 查看活动列表，支持按标题、受众、状态筛选
2. 编辑活动内容和设置（可切换内容类型）
3. 调整活动排序
4. 发布/取消发布活动
5. 禁用或删除活动（需要先取消发布）

## 注意事项

1. **唯一发布限制**：同一受众类型同时只能有一个活动发布，这是核心业务规则
2. **内容类型验证**：创建和更新活动时会严格验证内容类型与对应内容的一致性
3. **URL格式要求**：链接地址必须是有效的HTTP/HTTPS地址
4. **删除限制**：已发布的活动不能直接删除，必须先取消发布
5. **状态联动**：禁用已发布的活动时，系统会自动取消发布
6. **前端处理**：小程序端需要根据 `contentType` 字段判断如何处理活动内容
7. **安全考虑**：富文本内容需要进行安全处理，外部链接需要验证安全性
