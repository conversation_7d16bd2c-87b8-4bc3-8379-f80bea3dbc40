# 活动页管理接口文档

## 概述

小程序活动页管理功能，支持富文本方式维护活动信息列表，同一时间只能发布一个活动。

## 管理端接口

### 1. 查询活动列表

**接口地址：** `GET /admin/activities`

**请求参数：**
```json
{
  "current": 1,           // 当前页码，默认1
  "pageSize": 10,         // 每页数量，默认10
  "title": "活动标题",     // 可选，活动标题模糊查询
  "target": "用户端",      // 可选，受众筛选（用户端/员工端）
  "status": 1,            // 可选，状态筛选（0-禁用，1-启用）
  "isPublished": 1        // 可选，发布状态筛选（0-未发布，1-已发布）
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "春节活动",
        "content": "<p>活动详情...</p>",
        "target": "用户端",
        "coverImage": "https://example.com/cover.jpg",
        "status": 1,
        "isPublished": 1,
        "publishedAt": "2024-01-01T00:00:00.000Z",
        "sortOrder": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

### 2. 查询活动详情

**接口地址：** `GET /admin/activities/:id`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "content": "<p>活动详情...</p>",
    "target": "用户端",
    "coverImage": "https://example.com/cover.jpg",
    "status": 1,
    "isPublished": 1,
    "publishedAt": "2024-01-01T00:00:00.000Z",
    "sortOrder": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. 创建活动

**接口地址：** `POST /admin/activities`

**请求参数：**
```json
{
  "title": "活动标题",                    // 必填，最大100字符
  "content": "<p>富文本内容</p>",         // 必填，富文本内容
  "target": "用户端",                    // 必填，受众（用户端/员工端）
  "coverImage": "https://example.com/cover.jpg", // 可选，封面图片链接
  "status": 1,                          // 可选，状态（0-禁用，1-启用），默认1
  "sortOrder": 1                        // 可选，排序字段，默认自动设置
}
```

### 4. 更新活动

**接口地址：** `PUT /admin/activities/:id`

**请求参数：**（所有字段都是可选的）
```json
{
  "title": "新活动标题",
  "content": "<p>新的富文本内容</p>",
  "target": "员工端",
  "coverImage": "https://example.com/new-cover.jpg",
  "status": 1,
  "sortOrder": 2
}
```

### 5. 删除活动

**接口地址：** `DELETE /admin/activities/:id`

**注意：** 已发布的活动不能删除，需要先取消发布。

### 6. 更新活动状态

**接口地址：** `PUT /admin/activities/:id/status`

**请求参数：**
```json
{
  "status": 0  // 0-禁用，1-启用
}
```

**注意：** 如果要禁用已发布的活动，系统会自动先取消发布。

### 7. 发布活动

**接口地址：** `POST /admin/activities/:id/publish`

**功能说明：**
- 发布指定活动
- 同一受众（用户端/员工端）同时只能有一个活动发布
- 发布新活动时会自动取消同受众的其他已发布活动
- 只有启用状态的活动才能发布

### 8. 取消发布活动

**接口地址：** `POST /admin/activities/:id/unpublish`

### 9. 更新活动排序

**接口地址：** `PUT /admin/activities/:id/sort-order`

**请求参数：**
```json
{
  "sortOrder": 5
}
```

### 10. 查询当前发布的活动

**接口地址：** `GET /admin/activities/published/current`

**请求参数：**
```json
{
  "target": "用户端"  // 可选，默认"用户端"
}
```

## 小程序端接口

### 1. 获取当前发布的活动

**接口地址：** `GET /openapi/activities/current`

**请求参数：**
```json
{
  "target": "用户端"  // 可选，默认"用户端"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "content": "<p>活动详情...</p>",
    "coverImage": "https://example.com/cover.jpg",
    "publishedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**注意：** 如果没有发布的活动，返回 `data: null`

### 2. 获取活动详情

**接口地址：** `GET /openapi/activities/:id`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "content": "<p>活动详情...</p>",
    "coverImage": "https://example.com/cover.jpg",
    "publishedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**注意：** 只返回已发布且启用状态的活动，否则返回 `data: null`

## 数据库字段说明

### activities 表新增字段

- `isPublished`: TINYINT，是否发布（0-未发布，1-已发布）
- `publishedAt`: DATETIME，发布时间
- `sortOrder`: INT，排序字段，用于活动列表排序

## 业务规则

1. **唯一发布规则**：同一受众（用户端/员工端）同时只能有一个活动处于发布状态
2. **发布条件**：只有启用状态（status=1）的活动才能发布
3. **删除限制**：已发布的活动不能直接删除，需要先取消发布
4. **状态联动**：禁用已发布的活动时，系统会自动先取消发布
5. **排序规则**：活动列表按 sortOrder 升序，createdAt 降序排列
