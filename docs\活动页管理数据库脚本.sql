-- 活动页管理功能数据库迁移脚本
-- 为 activities 表添加新字段

-- 添加发布状态字段
ALTER TABLE `activities` 
ADD COLUMN `isPublished` TINYINT NOT NULL DEFAULT 0 COMMENT '是否发布（0-未发布，1-已发布）' AFTER `status`;

-- 添加发布时间字段
ALTER TABLE `activities` 
ADD COLUMN `publishedAt` DATETIME NULL COMMENT '发布时间' AFTER `isPublished`;

-- 添加排序字段
ALTER TABLE `activities` 
ADD COLUMN `sortOrder` INT NOT NULL DEFAULT 0 COMMENT '排序字段' AFTER `publishedAt`;

-- 创建索引以提高查询性能
CREATE INDEX `idx_activities_target_published` ON `activities` (`target`, `isPublished`);
CREATE INDEX `idx_activities_sort_order` ON `activities` (`sortOrder`);
CREATE INDEX `idx_activities_published_at` ON `activities` (`publishedAt`);

-- 为现有数据设置默认排序值
UPDATE `activities` SET `sortOrder` = `id` WHERE `sortOrder` = 0;

-- 注意：由于MySQL的限制，我们不能直接创建条件唯一索引
-- 但是应用层的逻辑已经确保了同一受众同时只能有一个已发布的活动
-- 如果需要数据库层面的强制约束，可以考虑使用触发器或存储过程
